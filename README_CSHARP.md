# FmDrv C# Port

This is a C# port of the fmdrv project - a very accurate C port of SBFMDRV CMF replayer.

## Conversion Summary

The original C project has been successfully converted to C# with the following changes:

### Files Converted

1. **fmdrv.c + fmdrv.h** → **FmDrv.cs**
   - Combined header and implementation into a single C# class
   - Converted C structs to C# classes
   - Replaced function pointers with C# delegates
   - Converted all `adlib_write()` calls to `OPL3_WriteRegBuffered()`
   - Converted all `adlib_getsample()` calls to `OPL3_GenerateStream()`

2. **playcmf.c** → **Program.cs**
   - Converted main application to C# console app
   - Replaced SDL audio with NAudio for real-time audio playback
   - Implemented custom `FmDriverWaveProvider` for NAudio integration
   - Converted file I/O to use .NET File APIs

3. **opl/opl.c** → **opl/OPL3.cs** (provided)
   - Updated namespace from `XargonRebuild.Sound` to `FmDrv`
   - Kept all OPL3 emulation functionality intact

### Key Changes Made

- **OPL Function Replacement**: All `adlib_write()` calls replaced with `OPL3_WriteRegBuffered()`
- **Audio System**: Replaced SDL2 audio with NAudio for cross-platform audio output
- **Memory Management**: Converted C pointers and arrays to C# arrays and managed memory
- **Function Pointers**: Converted to C# delegates for event handlers
- **Data Types**: Converted C types to appropriate C# types (uint8_t → byte, uint16_t → ushort, etc.)
- **Project Structure**: Created .NET 6.0 console application with NAudio dependency

### Project Structure

```
FmDrv/
├── FmDrv.csproj          # .NET project file
├── Program.cs             # Main console application
├── FmDrv.cs              # Core FM driver logic
└── opl/
    └── OPL3.cs           # OPL3 emulation (Nuked OPL3 C# port)
```

### Building and Running

```bash
# Build the project
dotnet build

# Run with a CMF file
dotnet run -- path/to/music.cmf
```

### Features Preserved

- Complete CMF file format support
- Accurate OPL3 emulation via Nuked OPL3
- All original MIDI event handling
- Instrument management
- Tempo and timing control
- Percussion mode support

### Audio Implementation

The audio system has been converted from SDL2 to NAudio:

- **FmDriverWaveProvider**: Custom NAudio wave provider that interfaces with the FM driver
- **Real-time Audio**: NAudio handles buffering and real-time audio output automatically
- **Cross-platform**: NAudio provides cross-platform audio support for Windows, Linux, and macOS
- **Low Latency**: Configurable latency (default 100ms) similar to the original SDL implementation

### Notes

- Audio output now uses NAudio for professional-quality real-time playback
- All core FM synthesis and MIDI playback functionality is preserved
- The conversion maintains the original's accuracy and structure
- Cross-platform compatibility through .NET 6.0 and NAudio

### License

Licensed under the Apache License, Version 2.0, same as the original project.
