/*
 * fmdrv - very accurate C# port of SBFMDRV CMF replayer.
 *
 * Copyright 2024 Sergei "x0r" Kolzun
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 * limitations under the License.
 */

using System;
using System.IO;
using System.Threading;
using NAudio.Wave;

namespace FmDrv
{
    // Custom WaveProvider for FM driver audio output
    // This replaces the SDL audio callback functionality from the original C code
    public class FmDriverWaveProvider : IWaveProvider
    {
        private readonly FmDriver fmDriver;
        private readonly WaveFormat waveFormat;

        public FmDriverWaveProvider(FmDriver fmDriver, int sampleRate)
        {
            this.fmDriver = fmDriver;
            // Mono 16-bit audio to match the original SDL setup
            this.waveFormat = new WaveFormat(sampleRate, 16, 1);
        }

        public WaveFormat WaveFormat => waveFormat;

        public int Read(byte[] buffer, int offset, int count)
        {
            // Calculate how many samples we can fit in the buffer
            int samplesRequested = count / 2; // 16-bit = 2 bytes per sample
            int samplesWritten = 0;

            for (int i = 0; i < samplesRequested && fmDriver.GetMusicStatus() != 0; i++)
            {
                short sample = fmDriver.SbfmRender();

                // Convert to little-endian bytes
                buffer[offset + samplesWritten * 2] = (byte)(sample & 0xFF);
                buffer[offset + samplesWritten * 2 + 1] = (byte)((sample >> 8) & 0xFF);
                samplesWritten++;
            }

            return samplesWritten * 2; // Return bytes written
        }
    }

    class Program
    {
        private const uint SamplingFreq = 48000;
        private const int BufferSamples = 16384;

        private static FmDriver fmDriver = null!;
        private static WaveOutEvent? waveOut;
        private static FmDriverWaveProvider? waveProvider;

        static void Main(string[] args)
        {
            if (args.Length < 1)
            {
                Console.WriteLine("Usage: FmDrv <filename>");
                Environment.Exit(-1);
            }

            byte[] data = LoadCmf(args[0]);
            if (data == null)
            {
                Console.WriteLine($"Can't load: {args[0]}");
                Environment.Exit(-1);
            }

            fmDriver = new FmDriver();
            fmDriver.SbfmInit(SamplingFreq);
            fmDriver.SbfmReset();

            // Parse CMF file structure
            ushort instOffset = FmDrvHelper.Read16LE(data, 0x06);
            ushort numInstruments = FmDrvHelper.Read16LE(data, 0x24);
            ushort musicOffset = FmDrvHelper.Read16LE(data, 0x08);
            ushort ticksPerQuarter = FmDrvHelper.Read16LE(data, 0x0c);

            // Extract instrument data
            byte[] instruments = new byte[numInstruments * 16];
            Array.Copy(data, instOffset, instruments, 0, numInstruments * 16);

            // Extract music data
            byte[] musicData = new byte[data.Length - musicOffset];
            Array.Copy(data, musicOffset, musicData, 0, musicData.Length);

            fmDriver.SbfmInstrument(instruments, numInstruments);
            fmDriver.SbfmSongSpeed((ushort)(0x1234dc / ticksPerQuarter));
            fmDriver.SbfmPlayMusic(musicData);

            // Initialize NAudio for playback
            try
            {
                waveProvider = new FmDriverWaveProvider(fmDriver, (int)SamplingFreq);
                waveOut = new WaveOutEvent();

                // Set buffer size similar to SDL (optional, NAudio has good defaults)
                waveOut.DesiredLatency = 100; // 100ms latency

                waveOut.Init(waveProvider);
                waveOut.Play();

                Console.WriteLine($"Playing: {args[0]}");
                Console.WriteLine("Press any key to stop...");

                // Wait for user input or music to finish
                while (fmDriver.GetMusicStatus() != 0)
                {
                    if (Console.KeyAvailable)
                    {
                        Console.ReadKey(true);
                        break;
                    }
                    Thread.Sleep(100);
                }

                Console.WriteLine("Playback stopped.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Audio error: {ex.Message}");
            }
            finally
            {
                // Clean up audio resources
                waveOut?.Stop();
                waveOut?.Dispose();
            }
        }

        private static byte[] LoadCmf(string filename)
        {
            try
            {
                return File.ReadAllBytes(filename);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading file: {ex.Message}");
                return null;
            }
        }
    }
}
