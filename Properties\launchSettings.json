{"profiles": {"FmDrv": {"commandName": "Project", "commandLineArgs": "test.cmf", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "FmDrv (No Args)": {"commandName": "Project", "commandLineArgs": "", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "FmDrv (Custom File)": {"commandName": "Project", "commandLineArgs": "path/to/your/music.cmf", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}}}